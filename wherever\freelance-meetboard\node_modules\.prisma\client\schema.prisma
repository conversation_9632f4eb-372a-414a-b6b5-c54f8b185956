// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Core application models
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(CLIENT)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // NextAuth relations
  accounts Account[]
  sessions Session[]

  // Application relations
  freelancerProfile   FreelancerProfile?
  clientMeetings      Meeting[]            @relation("ClientMeetings")
  freelancerMeetings  Meeting[]            @relation("FreelancerMeetings")
  sentMessages        Message[]            @relation("SentMessages")
  uploadedFiles       File[]
  givenReviews        Review[]             @relation("GivenReviews")
  receivedReviews     Review[]             @relation("ReceivedReviews")
  clientPayments      Payment[]            @relation("ClientPayments")
  freelancerPayments  Payment[]            @relation("FreelancerPayments")
  notifications       Notification[]
  clientDecisions     FreelancerDecision[] @relation("ClientDecisions")
  freelancerDecisions FreelancerDecision[] @relation("FreelancerDecisions")
}

model FreelancerProfile {
  id           String   @id @default(cuid())
  userId       String   @unique
  title        String
  description  String
  hourlyRate   Float
  skills       String // JSON string of skill tags for SQLite
  experience   String
  portfolio    String? // JSON string of portfolio items
  availability String? // JSON string for availability settings
  meetingLink  String? // Google Meet link or other meeting platform link
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Meeting {
  id           String        @id @default(cuid())
  clientId     String
  freelancerId String
  title        String
  description  String?
  scheduledAt  DateTime
  duration     Int // Duration in minutes
  status       MeetingStatus @default(PENDING)
  meetingUrl   String? // For video call links
  notes        String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  client     User      @relation("ClientMeetings", fields: [clientId], references: [id], onDelete: Cascade)
  freelancer User      @relation("FreelancerMeetings", fields: [freelancerId], references: [id], onDelete: Cascade)
  messages   Message[]
  files      File[]
  review     Review?
  payments   Payment[]
}

model Message {
  id        String   @id @default(cuid())
  meetingId String
  senderId  String
  content   String
  createdAt DateTime @default(now())

  meeting Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  sender  User    @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
}

model File {
  id           String   @id @default(cuid())
  meetingId    String
  uploaderId   String
  filename     String
  originalName String
  mimeType     String
  size         Int
  url          String
  createdAt    DateTime @default(now())

  meeting  Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  uploader User    @relation(fields: [uploaderId], references: [id], onDelete: Cascade)
}

model Review {
  id         String   @id @default(cuid())
  meetingId  String   @unique
  reviewerId String
  revieweeId String
  rating     Int // 1-5 stars
  comment    String?
  createdAt  DateTime @default(now())

  meeting  Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  reviewer User    @relation("GivenReviews", fields: [reviewerId], references: [id], onDelete: Cascade)
  reviewee User    @relation("ReceivedReviews", fields: [revieweeId], references: [id], onDelete: Cascade)
}

model Payment {
  id           String        @id @default(cuid())
  meetingId    String
  clientId     String
  freelancerId String
  amount       Float
  status       PaymentStatus @default(PENDING)
  description  String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  meeting    Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  client     User    @relation("ClientPayments", fields: [clientId], references: [id], onDelete: Cascade)
  freelancer User    @relation("FreelancerPayments", fields: [freelancerId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String // meeting_request, payment, review, etc.
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model FreelancerDecision {
  id             String   @id @default(cuid())
  clientId       String
  freelancerId   String
  decision       String // "approve" or "reject"
  feedback       String?
  projectDetails String? // JSON string with project info
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  client     User @relation("ClientDecisions", fields: [clientId], references: [id], onDelete: Cascade)
  freelancer User @relation("FreelancerDecisions", fields: [freelancerId], references: [id], onDelete: Cascade)

  @@unique([clientId, freelancerId])
}

// Enums
enum UserRole {
  CLIENT
  FREELANCER
}

enum MeetingStatus {
  PENDING
  CONFIRMED
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}
