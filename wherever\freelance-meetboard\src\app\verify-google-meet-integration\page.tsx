"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle, ExternalLink, Video, Calendar } from "lucide-react"
import { QuickMeetingGenerator } from "@/components/google-meet-embed"

export default function VerifyGoogleMeetIntegrationPage() {
  const { data: session, status } = useSession()
  const [profile, setProfile] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState<{
    hasMeetingLink: boolean
    googleAccountConnected: boolean
    profileActive: boolean
    recommendations: string[]
  } | null>(null)

  useEffect(() => {
    if (status === "loading") return
    if (!session) return
    
    fetchProfile()
  }, [session, status])

  const fetchProfile = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/freelancer/profile")
      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
        if (data.profile) {
          verifyIntegration(data.profile)
        }
      }
    } catch (error) {
      console.error("Error fetching profile:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const verifyIntegration = (profileData: any) => {
    const recommendations: string[] = []
    
    const hasMeetingLink = !!(profileData?.meetingLink)
    const googleAccountConnected = session?.user?.email?.includes('@') // Basic check
    const profileActive = profileData?.isActive

    if (!hasMeetingLink) {
      recommendations.push("Consider adding a permanent Google Meet link to your profile")
      recommendations.push("You can also use the Quick Meeting Generator for on-demand meetings")
    }

    if (!profileActive) {
      recommendations.push("Activate your freelancer profile")
    }

    recommendations.push("Test your Google Meet setup by creating a test meeting")
    recommendations.push("Make sure your Google account has Google Meet enabled")

    setVerificationStatus({
      hasMeetingLink,
      googleAccountConnected,
      profileActive,
      recommendations
    })
  }

  if (status === "loading" || isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (!session) {
    return <div className="text-center py-12">Please sign in to verify your Google Meet integration</div>
  }

  if (session.user.role !== "FREELANCER") {
    return <div className="text-center py-12">This page is only for freelancers</div>
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Google Meet Integration Verification</h1>
        <p className="text-muted-foreground">
          Verify your Google Meet setup for seamless client meetings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-6 w-6" />
            Google Meet Integration Status
          </CardTitle>
          <CardDescription>
            Check your Google Meet setup and get recommendations for improvement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Status Checks */}
            <div className="grid gap-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {verificationStatus?.googleAccountConnected ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">Google Account Connected</span>
                  </div>
                </div>
                <Badge variant={verificationStatus?.googleAccountConnected ? "default" : "destructive"}>
                  {verificationStatus?.googleAccountConnected ? "Connected" : "Not Connected"}
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {verificationStatus?.hasMeetingLink ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-yellow-500" />
                    )}
                    <span className="font-medium">Meeting Link Set</span>
                  </div>
                </div>
                <Badge variant={verificationStatus?.hasMeetingLink ? "default" : "secondary"}>
                  {verificationStatus?.hasMeetingLink ? "Set" : "Optional"}
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {verificationStatus?.profileActive ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">Profile Active</span>
                  </div>
                </div>
                <Badge variant={verificationStatus?.profileActive ? "default" : "destructive"}>
                  {verificationStatus?.profileActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>

            {/* Current Setup */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-medium mb-3">Current Setup</h3>
              <div className="space-y-2 text-sm">
                <p><strong>Email:</strong> {session.user.email}</p>
                <p><strong>Meeting Link:</strong> {profile?.meetingLink || "Not set"}</p>
                <p><strong>Profile Status:</strong> {profile?.isActive ? "Active" : "Inactive"}</p>
              </div>
            </div>

            {/* Quick Meeting Generator */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Quick Meeting Generator
              </h3>
              <p className="text-sm text-blue-800 mb-4">
                Test your Google Meet integration by generating a quick meeting link:
              </p>
              <QuickMeetingGenerator />
            </div>

            {/* Recommendations */}
            {verificationStatus?.recommendations && verificationStatus.recommendations.length > 0 && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="font-medium mb-3 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Recommendations
                </h3>
                <ul className="space-y-2 text-sm">
                  {verificationStatus.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-yellow-600">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button onClick={() => window.open('/dashboard/profile', '_blank')}>
                Edit Profile
              </Button>
              <Button variant="outline" onClick={() => window.open('https://meet.google.com', '_blank')}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Google Meet
              </Button>
              <Button variant="outline" onClick={fetchProfile}>
                Refresh Check
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>How to Set Up Google Meet Integration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Option 1: Permanent Meeting Room</h4>
              <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                <li>Go to <a href="https://meet.google.com" target="_blank" className="text-blue-600 hover:underline">meet.google.com</a></li>
                <li>Click "New meeting" → "Create a meeting for later"</li>
                <li>Copy the meeting link and add it to your profile</li>
                <li>Clients can join this room anytime for calls</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Option 2: On-Demand Meetings</h4>
              <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                <li>Leave the meeting link field empty in your profile</li>
                <li>Use the Quick Meeting Generator when clients want to book</li>
                <li>This creates a Google Calendar event with automatic Meet link</li>
                <li>More flexible for scheduling specific times</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
