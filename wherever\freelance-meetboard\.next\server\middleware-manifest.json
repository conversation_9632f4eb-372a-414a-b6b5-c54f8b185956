{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NfwmD35AZyMxKVXBueqi4IIx1bL2pcFQNd7sbxO4Kpk=", "__NEXT_PREVIEW_MODE_ID": "67cf7195c7b2cd506b3f36361ff1b45a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "de61ef70e8ce05121759e5f326a7c6c44fa1aacc88d89f33515cf94344f7d282", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9eee9c2e14d071e2ecf2e0e3d43e5064337c430ef7ef5e6b2625c7634be8e63b"}}}, "functions": {}, "sortedMiddleware": ["/"]}