{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NfwmD35AZyMxKVXBueqi4IIx1bL2pcFQNd7sbxO4Kpk=", "__NEXT_PREVIEW_MODE_ID": "014bd14e19924a188d27c15f66c1c817", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c6ae0d9d4351da00a775207563e21f293bfd495ed87477a05fe1903ea35c148c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0011facea1e3a723c6da3f81d540d9e4b047388c8ce66ab9ff750030e6968e6c"}}}, "functions": {}, "sortedMiddleware": ["/"]}