"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { GoogleMeetBookingButton, MeetingScheduler } from "@/components/google-meet-embed"
import { Star, DollarSign, Calendar, User, Briefcase, Award, Clock, Video } from "lucide-react"

interface FreelancerProfile {
  id: string
  userId: string
  title: string
  description: string
  hourlyRate: number
  skills: string[]
  experience: string
  portfolio: any[]
  availability: string
  meetingLink: string
  isActive: boolean
  user: {
    name: string
    email: string
  }
  reviews?: {
    rating: number
    comment: string
    reviewer: { name: string }
    createdAt: string
  }[]
  averageRating?: number
  totalReviews?: number
}

export default function FreelancerProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const freelancerId = params.id as string

  const [freelancer, setFreelancer] = useState<FreelancerProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showMeetingScheduler, setShowMeetingScheduler] = useState(false)

  useEffect(() => {
    fetchFreelancer()
  }, [freelancerId])

  const fetchFreelancer = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/freelancer/${freelancerId}/public`)
      if (response.ok) {
        const data = await response.json()
        setFreelancer(data.freelancer)
      } else {
        router.push("/dashboard/freelancers")
      }
    } catch (error) {
      console.error("Error fetching freelancer:", error)
      router.push("/dashboard/freelancers")
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (!freelancer) {
    return <div className="text-center py-12">Freelancer not found</div>
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-2 mb-6">
          <Button 
            variant="ghost" 
            onClick={() => router.back()}
          >
            ← Back
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Profile */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl">{freelancer.user.name}</CardTitle>
                      <CardDescription className="text-lg">{freelancer.title}</CardDescription>
                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          <span className="font-semibold">${freelancer.hourlyRate}/hr</span>
                        </div>
                        {freelancer.averageRating && (
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span>{freelancer.averageRating.toFixed(1)}</span>
                            <span className="text-muted-foreground">
                              ({freelancer.totalReviews} review{freelancer.totalReviews !== 1 ? 's' : ''})
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{freelancer.description}</p>
              </CardContent>
            </Card>

            {/* Skills */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Skills & Expertise
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {freelancer.skills.map((skill, index) => (
                    <Badge key={index} variant="secondary">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Experience */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Experience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="whitespace-pre-wrap">{freelancer.experience}</p>
              </CardContent>
            </Card>

            {/* Portfolio */}
            {freelancer.portfolio.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Portfolio</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {freelancer.portfolio.map((item, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <h4 className="font-medium">{item.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                        {item.url && (
                          <a 
                            href={item.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-primary hover:underline text-sm mt-2 inline-block"
                          >
                            View Project →
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Reviews */}
            {freelancer.reviews && freelancer.reviews.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Client Reviews
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {freelancer.reviews.map((review, index) => (
                      <div key={index} className="border-l-4 border-primary/20 pl-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating ? "text-yellow-400 fill-current" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium">{review.reviewer.name}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">"{review.comment}"</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(review.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar - Booking */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Discovery Call</CardTitle>
                <CardDescription>
                  Book a 15-minute call to discuss your project with {freelancer.user.name}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {freelancer.meetingLink ? (
                  <>
                    <GoogleMeetBookingButton
                      meetingLink={freelancer.meetingLink}
                      buttonText="📞 Join Google Meet"
                      className="w-full text-lg py-3"
                      config={{
                        name: session?.user?.name || "",
                        email: session?.user?.email || "",
                        notes: `Quick discovery call with ${session?.user?.name || "Client"} via Freelance MeetBoard`,
                        duration: 15
                      }}
                    />

                    <div className="bg-blue-50 p-3 rounded text-sm">
                      <p className="text-blue-700 flex items-center gap-2">
                        <Video className="h-4 w-4" />
                        This freelancer has a Google Meet room ready for instant calls
                      </p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-800 mb-2">✨ Simple 2-Step Process:</h4>
                      <ol className="text-sm text-green-700 space-y-2 list-decimal list-inside">
                        <li><strong>15-Minute Discovery Call:</strong> Discuss your project and see if you're a good fit</li>
                        <li><strong>Instant Decision:</strong>
                          <ul className="ml-4 mt-1 space-y-1">
                            <li>✅ <strong>Approve:</strong> Meeting Board opens immediately for collaboration</li>
                            <li>❌ <strong>Pass:</strong> Both move on to find better matches</li>
                          </ul>
                        </li>
                      </ol>
                      <p className="text-sm text-green-600 mt-3 font-medium">
                        No complex booking process - just quick decisions and instant collaboration!
                      </p>
                    </div>

                    <div className="border-t pt-4">
                      <p className="text-sm text-muted-foreground mb-3">
                        Already had a discovery call with {freelancer.user.name}?
                      </p>
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => router.push(`/dashboard/approve-freelancer/${freelancer.userId}`)}
                      >
                        💭 Provide Feedback & Decision
                      </Button>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowMeetingScheduler(!showMeetingScheduler)}
                      >
                        {showMeetingScheduler ? "Hide" : "Show"} Meeting Scheduler
                      </Button>
                    </div>

                    {showMeetingScheduler && (
                      <div className="border rounded-lg overflow-hidden">
                        <MeetingScheduler
                          freelancerName={freelancer.user.name}
                          freelancerEmail={freelancer.user.email}
                          onScheduleRequest={async (meetingData) => {
                            // Handle meeting request - could send email or create calendar event
                            alert(`Meeting request sent to ${freelancer.user.name}!`)
                          }}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  <div className="space-y-4">
                    <GoogleMeetBookingButton
                      buttonText="📞 Request Discovery Call"
                      className="w-full text-lg py-3"
                      config={{
                        name: session?.user?.name || "",
                        email: session?.user?.email || "",
                        notes: `Quick discovery call with ${session?.user?.name || "Client"} via Freelance MeetBoard`,
                        duration: 15
                      }}
                    />

                    <div className="bg-yellow-50 p-3 rounded text-sm">
                      <p className="text-yellow-700 flex items-center gap-2">
                        <Video className="h-4 w-4" />
                        This will create a Google Calendar event with a Meet link
                      </p>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowMeetingScheduler(!showMeetingScheduler)}
                      >
                        {showMeetingScheduler ? "Hide" : "Show"} Meeting Scheduler
                      </Button>
                    </div>

                    {showMeetingScheduler && (
                      <MeetingScheduler
                        freelancerName={freelancer.user.name}
                        freelancerEmail={freelancer.user.email}
                        onScheduleRequest={async (meetingData) => {
                          alert(`Meeting request sent to ${freelancer.user.name}!`)
                        }}
                      />
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Availability */}
            {freelancer.availability && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Availability
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{freelancer.availability}</p>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Hourly Rate</span>
                  <span className="font-medium">${freelancer.hourlyRate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Skills</span>
                  <span className="font-medium">{freelancer.skills.length}</span>
                </div>
                {freelancer.averageRating && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Rating</span>
                    <span className="font-medium">{freelancer.averageRating.toFixed(1)}/5</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Portfolio</span>
                  <span className="font-medium">{freelancer.portfolio.length} project{freelancer.portfolio.length !== 1 ? 's' : ''}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
