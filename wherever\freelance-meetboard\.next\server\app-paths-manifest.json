{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/meetings/route": "app/api/meetings/route.js", "/api/freelancers/route": "app/api/freelancers/route.js", "/api/freelancer/[id]/public/route": "app/api/freelancer/[id]/public/route.js", "/api/notifications/[id]/route": "app/api/notifications/[id]/route.js", "/freelancer/[id]/page": "app/freelancer/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/freelancers/page": "app/dashboard/freelancers/page.js", "/dashboard/meetings/page": "app/dashboard/meetings/page.js", "/dashboard/notifications/page": "app/dashboard/notifications/page.js"}