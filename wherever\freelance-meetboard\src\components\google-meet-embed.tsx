"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, Clock, Video, ExternalLink } from "lucide-react"

// Google Meet booking button - opens Google Calendar to create a new event
interface GoogleMeetBookingButtonProps {
  meetingLink?: string
  buttonText?: string
  className?: string
  config?: {
    name?: string
    email?: string
    notes?: string
    duration?: number // in minutes
  }
}

export function GoogleMeetBookingButton({
  meetingLink,
  buttonText = "Book a Meeting",
  className = "",
  config
}: GoogleMeetBookingButtonProps) {
  const handleBooking = () => {
    // If a specific Google Meet link is provided, open it directly
    if (meetingLink && meetingLink.includes('meet.google.com')) {
      window.open(meetingLink, '_blank')
      return
    }

    // Otherwise, create a Google Calendar event with Google Meet
    const startDate = new Date()
    startDate.setHours(startDate.getHours() + 1) // Default to 1 hour from now
    
    const endDate = new Date(startDate)
    endDate.setMinutes(endDate.getMinutes() + (config?.duration || 15)) // Default 15 minutes

    const params = new URLSearchParams({
      action: 'TEMPLATE',
      text: `Discovery Call${config?.name ? ` with ${config.name}` : ''}`,
      dates: `${formatGoogleCalendarDate(startDate)}/${formatGoogleCalendarDate(endDate)}`,
      details: config?.notes || 'Meeting scheduled via Freelance MeetBoard',
      location: 'Google Meet (link will be generated)',
      add: config?.email || '',
      conf: '1' // This adds Google Meet automatically
    })

    const calendarUrl = `https://calendar.google.com/calendar/render?${params.toString()}`
    window.open(calendarUrl, '_blank')
  }

  return (
    <Button
      onClick={handleBooking}
      className={`inline-flex items-center justify-center ${className}`}
    >
      <Video className="h-4 w-4 mr-2" />
      {buttonText}
    </Button>
  )
}

// Simple Google Meet link button - for when freelancer provides a direct meet link
interface GoogleMeetLinkButtonProps {
  meetingLink: string
  buttonText?: string
  className?: string
  children?: React.ReactNode
}

export function GoogleMeetLinkButton({
  meetingLink,
  buttonText = "Join Meeting",
  className = "",
  children
}: GoogleMeetLinkButtonProps) {
  const handleJoinMeeting = () => {
    window.open(meetingLink, '_blank')
  }

  return (
    <Button
      onClick={handleJoinMeeting}
      className={`inline-flex items-center justify-center ${className}`}
    >
      <Video className="h-4 w-4 mr-2" />
      {children || buttonText}
    </Button>
  )
}

// Meeting scheduler component - allows clients to request a meeting time
interface MeetingSchedulerProps {
  freelancerName: string
  freelancerEmail?: string
  onScheduleRequest: (meetingData: {
    preferredDate: string
    preferredTime: string
    duration: number
    notes: string
  }) => void
}

export function MeetingScheduler({
  freelancerName,
  freelancerEmail,
  onScheduleRequest
}: MeetingSchedulerProps) {
  const [preferredDate, setPreferredDate] = useState("")
  const [preferredTime, setPreferredTime] = useState("")
  const [duration, setDuration] = useState(15)
  const [notes, setNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      await onScheduleRequest({
        preferredDate,
        preferredTime,
        duration,
        notes
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Schedule a Meeting
        </CardTitle>
        <CardDescription>
          Request a meeting time with {freelancerName}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date">Preferred Date</Label>
              <Input
                id="date"
                type="date"
                value={preferredDate}
                onChange={(e) => setPreferredDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
            <div>
              <Label htmlFor="time">Preferred Time</Label>
              <Input
                id="time"
                type="time"
                value={preferredTime}
                onChange={(e) => setPreferredTime(e.target.value)}
                required
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="duration">Duration (minutes)</Label>
            <select
              id="duration"
              value={duration}
              onChange={(e) => setDuration(Number(e.target.value))}
              className="w-full p-2 border rounded-md"
            >
              <option value={15}>15 minutes</option>
              <option value={30}>30 minutes</option>
              <option value={45}>45 minutes</option>
              <option value={60}>60 minutes</option>
            </select>
          </div>

          <div>
            <Label htmlFor="notes">Notes (optional)</Label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Brief description of what you'd like to discuss..."
              className="w-full p-2 border rounded-md"
              rows={3}
            />
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? "Sending Request..." : "Request Meeting"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

// Utility function to format dates for Google Calendar
function formatGoogleCalendarDate(date: Date): string {
  return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
}

// Quick meeting link generator for freelancers
export function QuickMeetingGenerator() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLink, setGeneratedLink] = useState("")

  const generateMeetingLink = async () => {
    setIsGenerating(true)
    
    // In a real implementation, this would call Google Meet API
    // For now, we'll create a Google Calendar event that generates a Meet link
    const startDate = new Date()
    startDate.setHours(startDate.getHours() + 1)
    
    const endDate = new Date(startDate)
    endDate.setMinutes(endDate.getMinutes() + 30)

    const params = new URLSearchParams({
      action: 'TEMPLATE',
      text: 'Quick Meeting',
      dates: `${formatGoogleCalendarDate(startDate)}/${formatGoogleCalendarDate(endDate)}`,
      details: 'Quick meeting generated via Freelance MeetBoard',
      conf: '1'
    })

    const calendarUrl = `https://calendar.google.com/calendar/render?${params.toString()}`
    setGeneratedLink(calendarUrl)
    setIsGenerating(false)
  }

  return (
    <div className="space-y-4">
      <Button onClick={generateMeetingLink} disabled={isGenerating}>
        {isGenerating ? "Generating..." : "Generate Quick Meeting Link"}
      </Button>
      
      {generatedLink && (
        <div className="p-4 bg-muted rounded-lg">
          <p className="text-sm font-medium mb-2">Generated Meeting Link:</p>
          <div className="flex gap-2">
            <Input value={generatedLink} readOnly className="flex-1" />
            <Button
              size="sm"
              onClick={() => navigator.clipboard.writeText(generatedLink)}
            >
              Copy
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.open(generatedLink, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
