"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from "lucide-react"

export default function VerifyCalIntegrationPage() {
  const { data: session, status } = useSession()
  const [profile, setProfile] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState<{
    hasCalLink: boolean
    emailMatch: boolean
    accountExists: boolean
    recommendations: string[]
  } | null>(null)

  useEffect(() => {
    if (status === "loading") return
    if (!session) return
    
    fetchProfile()
  }, [session, status])

  const fetchProfile = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/freelancer/profile")
      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
        verifyIntegration(data.profile)
      }
    } catch (error) {
      console.error("Error fetching profile:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const verifyIntegration = (profileData: any) => {
    const recommendations: string[] = []
    
    const hasCalLink = !!(profileData?.calLink)
    const emailMatch = session?.user.email === session?.user.email // Always true for same user
    const accountExists = hasCalLink // Simplified check

    if (!hasCalLink) {
      recommendations.push("Add your Cal.com username to your profile")
      recommendations.push("Make sure you create a Cal.com account first")
    }

    if (!profileData?.isActive) {
      recommendations.push("Activate your freelancer profile")
    }

    if (hasCalLink && !profileData.calLink.includes(session?.user.email?.split('@')[0])) {
      recommendations.push("Consider using a Cal.com username that matches your email for easier identification")
    }

    recommendations.push("Test your Cal.com booking link to ensure it works")
    recommendations.push("Create a 15-minute 'Discovery Call' event type in Cal.com")

    setVerificationStatus({
      hasCalLink,
      emailMatch,
      accountExists,
      recommendations
    })
  }

  if (status === "loading" || isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (!session) {
    return <div className="text-center py-12">Please sign in to verify your Cal.com integration</div>
  }

  if (session.user.role !== "FREELANCER") {
    return <div className="text-center py-12">This page is only for freelancers</div>
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Cal.com Integration Verification</CardTitle>
          <CardDescription>
            Check if your Cal.com account is properly integrated with MeetBoard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Account Info */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium mb-2">Your MeetBoard Account</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Name:</strong> {session.user.name}</p>
                <p><strong>Email:</strong> {session.user.email}</p>
                <p><strong>Role:</strong> {session.user.role}</p>
              </div>
            </div>

            {/* Verification Results */}
            {verificationStatus && (
              <div className="space-y-3">
                <h3 className="font-medium">Integration Status</h3>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {verificationStatus.hasCalLink ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Cal.com Link Added</span>
                    {verificationStatus.hasCalLink && (
                      <Badge variant="secondary">{profile?.calLink}</Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Email Account Match</span>
                    <Badge variant="secondary">{session.user.email}</Badge>
                  </div>

                  <div className="flex items-center gap-2">
                    {profile?.isActive ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Profile Active</span>
                  </div>
                </div>
              </div>
            )}

            {/* Recommendations */}
            {verificationStatus?.recommendations && verificationStatus.recommendations.length > 0 && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                  <h3 className="font-medium text-yellow-800">Recommendations</h3>
                </div>
                <ul className="space-y-1 text-sm text-yellow-700">
                  {verificationStatus.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-yellow-600">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button onClick={() => window.open('/dashboard/profile', '_blank')}>
                Edit Profile
              </Button>
              <Button variant="outline" onClick={() => window.open('https://cal.com', '_blank')}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Cal.com
              </Button>
              <Button variant="outline" onClick={fetchProfile}>
                Refresh Check
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Integration Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Step-by-Step Integration Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">✅ Critical Success Factor</h4>
              <p className="text-sm text-green-700">
                <strong>Use the SAME Google account</strong> for both MeetBoard ({session.user.email}) and Cal.com. 
                This is essential for the integration to work.
              </p>
            </div>

            <ol className="space-y-3 text-sm">
              <li className="flex gap-3">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">1</span>
                <div>
                  <p className="font-medium">Create Cal.com Account</p>
                  <p className="text-muted-foreground">Go to cal.com and sign up with {session.user.email}</p>
                </div>
              </li>
              
              <li className="flex gap-3">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">2</span>
                <div>
                  <p className="font-medium">Set Up Event Type</p>
                  <p className="text-muted-foreground">Create a "Discovery Call" event type (15 minutes)</p>
                </div>
              </li>
              
              <li className="flex gap-3">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">3</span>
                <div>
                  <p className="font-medium">Get Your Username</p>
                  <p className="text-muted-foreground">Copy your Cal.com username (e.g., "john-doe")</p>
                </div>
              </li>
              
              <li className="flex gap-3">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">4</span>
                <div>
                  <p className="font-medium">Add to MeetBoard</p>
                  <p className="text-muted-foreground">Paste your username in your MeetBoard profile</p>
                </div>
              </li>
              
              <li className="flex gap-3">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">5</span>
                <div>
                  <p className="font-medium">Test Integration</p>
                  <p className="text-muted-foreground">Have someone test booking a discovery call</p>
                </div>
              </li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
