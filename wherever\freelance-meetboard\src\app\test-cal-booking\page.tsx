"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Calendar, CheckCircle, AlertCircle } from "lucide-react"

export default function TestCalBookingPage() {
  const [freelancerEmail, setFreelancerEmail] = useState("")
  const [clientEmail, setClientEmail] = useState("")
  const [clientName, setClientName] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleTestBooking = async () => {
    if (!freelancerEmail || !clientEmail || !clientName) {
      alert("Please fill in all fields")
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch("/api/test-cal-booking", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          freelancerEmail,
          clientEmail,
          clientName
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        error: "Failed to create test booking"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Test Cal.com Booking Workflow</h1>
        <p className="text-muted-foreground">
          Simulate a Cal.com booking to test the freelancer approval workflow
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Simulate Cal.com Booking
          </CardTitle>
          <CardDescription>
            This will create a test meeting that requires freelancer approval
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="freelancerEmail">Freelancer Email</Label>
            <Input
              id="freelancerEmail"
              type="email"
              placeholder="<EMAIL>"
              value={freelancerEmail}
              onChange={(e) => setFreelancerEmail(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              The freelancer who will receive the meeting request
            </p>
          </div>

          <div>
            <Label htmlFor="clientEmail">Client Email</Label>
            <Input
              id="clientEmail"
              type="email"
              placeholder="<EMAIL>"
              value={clientEmail}
              onChange={(e) => setClientEmail(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              The client who is booking the meeting
            </p>
          </div>

          <div>
            <Label htmlFor="clientName">Client Name</Label>
            <Input
              id="clientName"
              placeholder="John Doe"
              value={clientName}
              onChange={(e) => setClientName(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              The client's display name
            </p>
          </div>

          <Button 
            onClick={handleTestBooking} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? "Creating Test Booking..." : "Create Test Cal.com Booking"}
          </Button>

          {result && (
            <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              <div className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={result.success ? "text-green-800" : "text-red-800"}>
                  {result.success ? (
                    <div>
                      <p className="font-medium">Test booking created successfully!</p>
                      <p className="text-sm mt-1">
                        The freelancer should now see a pending meeting request in their dashboard.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="font-medium">Failed to create test booking</p>
                      <p className="text-sm mt-1">{result.error}</p>
                      {result.details && (
                        <pre className="text-xs mt-2 bg-red-100 p-2 rounded overflow-auto">
                          {result.details}
                        </pre>
                      )}
                    </div>
                  )}
                </AlertDescription>
              </div>
            </Alert>
          )}

          {result?.success && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Next Steps:</h4>
              <ol className="text-sm space-y-1 list-decimal list-inside text-blue-800">
                <li>The freelancer ({freelancerEmail}) should receive an email notification</li>
                <li>The freelancer should see a pending meeting in their dashboard</li>
                <li>The freelancer can approve or decline the meeting</li>
                <li>Once approved, the client will be notified</li>
              </ol>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>How to Test the Full Workflow</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Step 1: Create Test Booking</h4>
              <p className="text-muted-foreground">
                Use the form above to simulate a Cal.com booking. Make sure the freelancer email 
                matches a real freelancer account in your system.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Step 2: Check Freelancer Dashboard</h4>
              <p className="text-muted-foreground">
                Log in as the freelancer and go to the meetings dashboard. You should see a 
                pending meeting request with "Accept" and "Decline" buttons.
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Step 3: Test Approval</h4>
              <p className="text-muted-foreground">
                Click "Accept Meeting" to approve the request. The client should receive a 
                confirmation notification.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
